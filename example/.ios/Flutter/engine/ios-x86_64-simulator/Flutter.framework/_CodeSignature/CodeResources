<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Flutter.h</key>
		<data>
		Jfv2Vo3itUyjWGSxXIEV/9Skri8=
		</data>
		<key>Headers/FlutterAppDelegate.h</key>
		<data>
		QU3ZohDucENpee9k2C4s7Ovkwxw=
		</data>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<data>
		IiB4oBoahOzoEx3SPBG3s8/FPzc=
		</data>
		<key>Headers/FlutterCallbackCache.h</key>
		<data>
		7bvv6icUG3Vh8xBTZDi569tVAyk=
		</data>
		<key>Headers/FlutterChannels.h</key>
		<data>
		g1v827SSGL9siIwt+VqLxDPlz2s=
		</data>
		<key>Headers/FlutterCodecs.h</key>
		<data>
		eBPdm9rJrQ55L4k97sWSW6Xjh6c=
		</data>
		<key>Headers/FlutterDartProject.h</key>
		<data>
		KV/kYQEVGGR8R5A47Y8un4FVpyM=
		</data>
		<key>Headers/FlutterEngine.h</key>
		<data>
		tyGI0yDeQ1SJhyNBDYLjEhVevig=
		</data>
		<key>Headers/FlutterEngineGroup.h</key>
		<data>
		3CQYrnnKlnKuRoN9RMAfeIdDBro=
		</data>
		<key>Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		XnDDN+yQj6qLXTuhI0tgTMDbtbI=
		</data>
		<key>Headers/FlutterMacros.h</key>
		<data>
		I9N4VAVhaoHjYjsRdmqSzEYJgN4=
		</data>
		<key>Headers/FlutterPlatformViews.h</key>
		<data>
		JofRibXJB+HPxhe0SAphfoKFSTE=
		</data>
		<key>Headers/FlutterPlugin.h</key>
		<data>
		+fobtZzAsiVeUQCTJcgII0nydHs=
		</data>
		<key>Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		K+zH/EWMTvLAMWxrQi5N0H8ITu0=
		</data>
		<key>Headers/FlutterTexture.h</key>
		<data>
		6MpKfd6Ff1YhpiuPp6cNh9VC2JY=
		</data>
		<key>Headers/FlutterViewController.h</key>
		<data>
		HnjrTJBnh6roH17ANtquMUKFNEE=
		</data>
		<key>Info.plist</key>
		<data>
		jAcF0X1gB3jtvTrfkmV31a5+HQU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>icudtl.dat</key>
		<data>
		GQZCpXMybn3RCh1MXi5WGSJg97k=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Flutter.h</key>
		<dict>
			<key>hash</key>
			<data>
			Jfv2Vo3itUyjWGSxXIEV/9Skri8=
			</data>
			<key>hash2</key>
			<data>
			uwm8JZgId56AcAI6HgoYvB86L3U5XLTdogukvzdieH0=
			</data>
		</dict>
		<key>Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			QU3ZohDucENpee9k2C4s7Ovkwxw=
			</data>
			<key>hash2</key>
			<data>
			ZE5n3wyEphmq0NvXEk+TgX9+IH2WWIRdDoZj+8Zmu+E=
			</data>
		</dict>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash</key>
			<data>
			IiB4oBoahOzoEx3SPBG3s8/FPzc=
			</data>
			<key>hash2</key>
			<data>
			06K0zk/qdPPWMfTjZZ6kwk9SxXF4dYAjCcrmLOEdQLE=
			</data>
		</dict>
		<key>Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			7bvv6icUG3Vh8xBTZDi569tVAyk=
			</data>
			<key>hash2</key>
			<data>
			eY8bxazAyq1tt4tOITnPXoWgBSzFNNtgbLwGkMdVpdw=
			</data>
		</dict>
		<key>Headers/FlutterChannels.h</key>
		<dict>
			<key>hash</key>
			<data>
			g1v827SSGL9siIwt+VqLxDPlz2s=
			</data>
			<key>hash2</key>
			<data>
			zkESAs3TsdMXoCRnsQuOsCnUT3XfOtdlzW/6r1NBmMA=
			</data>
		</dict>
		<key>Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash</key>
			<data>
			eBPdm9rJrQ55L4k97sWSW6Xjh6c=
			</data>
			<key>hash2</key>
			<data>
			Cm7NIJyBWt8uDf6CVYR6rgQB+ED9Nq8aE4KMBx2bhqw=
			</data>
		</dict>
		<key>Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash</key>
			<data>
			KV/kYQEVGGR8R5A47Y8un4FVpyM=
			</data>
			<key>hash2</key>
			<data>
			oh1zoNJHh3WYpypd0pFtVfMe26Vi8LBCgWWufB9DCUc=
			</data>
		</dict>
		<key>Headers/FlutterEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			tyGI0yDeQ1SJhyNBDYLjEhVevig=
			</data>
			<key>hash2</key>
			<data>
			KDfw4t6B/H/rDn2Gp3fIZJzXIf2vf58HkMPANxsapGY=
			</data>
		</dict>
		<key>Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash</key>
			<data>
			3CQYrnnKlnKuRoN9RMAfeIdDBro=
			</data>
			<key>hash2</key>
			<data>
			A0v+KrD0LG5+JXDDo8q8f/dCHcwDp6goYox7ND9QgRE=
			</data>
		</dict>
		<key>Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash</key>
			<data>
			XnDDN+yQj6qLXTuhI0tgTMDbtbI=
			</data>
			<key>hash2</key>
			<data>
			sELlVsLARG1gBlPndKt24VxGVmBMgcXWeShflcVtZBQ=
			</data>
		</dict>
		<key>Headers/FlutterMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			I9N4VAVhaoHjYjsRdmqSzEYJgN4=
			</data>
			<key>hash2</key>
			<data>
			7FrU4ZPRKob2HMUIRVBrtZ/QBRs2QL0JUN4oJmEaZs0=
			</data>
		</dict>
		<key>Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash</key>
			<data>
			JofRibXJB+HPxhe0SAphfoKFSTE=
			</data>
			<key>hash2</key>
			<data>
			c4TS8HplkxEc+09dBX5h+BZ+vkI9QJU/3ljud7WmdTM=
			</data>
		</dict>
		<key>Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			+fobtZzAsiVeUQCTJcgII0nydHs=
			</data>
			<key>hash2</key>
			<data>
			0A5Ks7tge5SF1tnhBes7S5quCY4A6SDz449L1EhWzdI=
			</data>
		</dict>
		<key>Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			K+zH/EWMTvLAMWxrQi5N0H8ITu0=
			</data>
			<key>hash2</key>
			<data>
			Qh6v1uCtFmgZuTZJQxeFnZJemL/fcZbGzNFKH+Q+R9k=
			</data>
		</dict>
		<key>Headers/FlutterTexture.h</key>
		<dict>
			<key>hash</key>
			<data>
			6MpKfd6Ff1YhpiuPp6cNh9VC2JY=
			</data>
			<key>hash2</key>
			<data>
			oyJl6rLO2BXOONxItYOvj8ybnSoSbZplBWLb10M9GI0=
			</data>
		</dict>
		<key>Headers/FlutterViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			HnjrTJBnh6roH17ANtquMUKFNEE=
			</data>
			<key>hash2</key>
			<data>
			g9kFGn3cX4EiJery7mzGg05qDMpvHeJEOeWCttsMfXE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wJV5dCKEGl+FAtDc8wJJh/fvKXs=
			</data>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>icudtl.dat</key>
		<dict>
			<key>hash</key>
			<data>
			GQZCpXMybn3RCh1MXi5WGSJg97k=
			</data>
			<key>hash2</key>
			<data>
			PxmoJrR/h+ORdYBLNxblG7h3l/244/4/McWAKWZ0TDY=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
