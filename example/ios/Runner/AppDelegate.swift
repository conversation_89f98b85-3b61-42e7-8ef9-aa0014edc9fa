import UIKit
import XY<PERSON><PERSON><PERSON>r


@UIApplicationMain
@objc class AppDelegate: UIResponder, UIApplicationDelegate {
    
    var window: UIWindow?
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        self.window = UIWindow(frame: UIScreen.main.bounds)
        self.window?.backgroundColor = UIColor.white
        self.window?.makeKeyAndVisible()
        
        XYYXXBridgeRegistry.bridgeInterface = FlutterBridgeInterface()
        let navigation = UINavigationController(rootViewController: TestViewController())
        
        self.window?.rootViewController = navigation
        return true
    }
}
