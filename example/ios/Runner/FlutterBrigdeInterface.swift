//
//  FlutterBridgeInterface.swift
//  Runner
//
//  Created by FW on 2020/11/6.
//

import Foundation
import XYYContainer

class FlutterBridgeInterface: NSObject, XYYXXBridgeInterface {
    func uploadImage(withPath path: String, withImageData imageData: Data, withExtraParams extraParams: [AnyHashable : Any]?, withHandler handler: @escaping (String?, Error?) -> Void) {
        
    }
    
    func requestSingleLocation(handler: @escaping (String?, Double, Double, Error?) -> Void) {

    }

    func request(withPath path: String,
                 withMethod method: String?,
                 withContentType contentType: String?,
                 withParameters parameters: Any?,
                 withHeaders addHeader: [String : String]?,
                 andHandler handler: @escaping (String?, Error?) -> Void)
    {
        handler("request: path:\(path)", nil)
    }

    func uploadImage(withPath path: String,
                     withImageData imageData: Data,
                     withHandler handler: @escaping (String?, Error?) -> Void)
    {
        handler("imageUrlaaa", nil)
    }

    func showToast(withMessage message: String, with toastType: XYYXXToastType) {
        print("Toast : ", message)
        print("styleType : ", toastType)
    }

}
