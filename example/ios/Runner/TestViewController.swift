//
//  Test\ViewController.swift
//  Runner
//
//  Created by FW on 2020/12/14.
//

import UIKit
import XYYContainer

class TestViewController: UIViewController {

    var jumpCount: Int = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        
        let button = UIButton(type: .custom)
        button.setTitle("跳转", for: .normal)
        button.setTitleColor(UIColor.black, for: .normal)
        button.addTarget(self, action: #selector(jumpFlutter), for: .touchUpInside)
        button.frame = CGRect(x: 100, y: 100, width: 100, height: 100)
        self.view.addSubview(button)
        
    }
    
    @objc func jumpFlutter() {
        
        XYYXXRouterManager.openPage(for: "/Test1")
        let controller = XYYXXFlutterViewController(router: self.jumpCount % 2 == 0 ? "/Test1" : "/Test2", reply: nil)
        self.navigationController?.pushViewController(controller, animated: true)
        
        self.jumpCount += 1
    }

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destination.
        // Pass the selected object to the new view controller.
    }
    */

}
