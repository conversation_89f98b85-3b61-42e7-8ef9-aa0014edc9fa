import 'dart:async';

import 'package:container_example/test_page1.dart';
import 'package:container_example/test_page2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/toast/toast_channel.dart';

void main() {
  XYYContainer.init(routes, appBuilder: (
    context,
    navigatorKey,
    initialRoutePath,
    customObserver,
    onGenerateRoute,
    onGenerateInitialRoutes,
  ) {
    print('guan:appbuilder:$initialRoutePath');
    return MaterialApp(
      initialRoute: initialRoutePath,
      debugShowCheckedModeBanner: false,
      onGenerateRoute: onGenerateRoute,
      onGenerateInitialRoutes: onGenerateInitialRoutes,
      navigatorObservers: [customObserver],
    );
  });
}

final routes = {
  '/': (context, arguments) => TestPhotoBridgePage(),
  '/Test1': (context, arguments) => TestPage1(),
  '/Test2': (context, arguments) => TestPage2(),
};

class TestPhotoBridgePage extends StatefulWidget {
  @override
  _TestPhotoBridgeState createState() => _TestPhotoBridgeState();
}

class _TestPhotoBridgeState extends State<TestPhotoBridgePage> {
  List<String> _localIds = [];
  List<String> _carmeraLocalIds = [];
  List<String> _imageUrls = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('测试桥'),
        ),
        body: Column(
          children: [
            FlatButton(
              onPressed: () {
                this.openAlbum();
              },
              child: Text('相册'),
            ),
            Text(_localIds.join(",")),
            FlatButton(
              onPressed: () {
                this.openCamera();
              },
              child: Text('相机'),
            ),
            Text(_carmeraLocalIds.join(",")),
            FlatButton(
              onPressed: () {
                this.uploadImage();
              },
              child: Text('图片上传'),
            ),
            Text(_imageUrls.join(",")),
            FlatButton(
              onPressed: () {
                XYYContainer.toastChannel.toast(
                  '这里是Toast信息',
                  type: ToastType.Normal,
                );
              },
              child: Text('Toast'),
            ),
            FlatButton(
              onPressed: () async {
                bool success = await (XYYContainer.storageChannel
                        .putAll({'name': '哈哈哈'}, space: 'account')
                    as FutureOr<bool>);
                XYYContainer.toastChannel.toast(success ? '成功' : '失败');
              },
              child: Text('存数据'),
            ),
            FlatButton(
              onPressed: () async {
                Map<String, dynamic>? map =
                    await XYYContainer.storageChannel.getAll(space: 'account');
                XYYContainer.toastChannel.toast(map.toString());
              },
              child: Text('取数据'),
            ),
            FlatButton(
              onPressed: () {
                XYYContainer.storageChannel.delete('name', space: 'account');
              },
              child: Text('删数据'),
            ),
            FlatButton(
              onPressed: () {
                parseJson();
              },
              child: Text('解析json'),
            ),
          ],
        ));
  }

  void openAlbum() async {
    List<String>? ids = [];
    try {
      ids = await (XYYContainer.selectPhotoChannel
          .selectPhotoForAlbum(imageCount: 2) as FutureOr<List<String>>);
    } on PlatformException catch (e) {
      print(e);
    }
    setState(() {
      this._localIds = ids ?? [];
    });
  }

  void openCamera() async {
    List<String>? ids = [];
    try {
      ids = await (XYYContainer.selectPhotoChannel.selectPhotoForCamera()
          as FutureOr<List<String>>);
    } on PlatformException catch (e) {
      print(e);
    }
    setState(() {
      this._carmeraLocalIds = ids ?? [];
    });
  }

  void uploadImage() async {
    List<String>? ids = [];
    try {
      ids = await (XYYContainer.photoForAlbumWithUpload(
        '/app/crm/schedule/uploadLicenseAuditImg?sysUserId=11803',
        imageCount: 2,
      ) as FutureOr<List<String>>);
    } on PlatformException catch (e) {
      print(e);
    }
    setState(() {
      this._imageUrls = ids ?? [];
    });
  }

  void parseJson() async {}
}
