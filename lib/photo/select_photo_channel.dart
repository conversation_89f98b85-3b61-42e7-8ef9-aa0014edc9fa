import 'package:flutter/services.dart';
import '../tools/tools.dart';

/// 调用方式
enum SelectPhotoType { Camera, Album }

class SelectPhotoChannel {
  MethodChannel _brigdeChannel;

  SelectPhotoChannel.fromChannel(MethodChannel brigdeChannel)
      : _brigdeChannel = brigdeChannel;

  Future<List<String>?> selectPhoto(SelectPhotoType type,
      {int? count = 1}) async {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['type'] = enumToString(type);

      if (count != null) {
        requestParam['imageCount'] = count;
      }

      return await this
          ._brigdeChannel
          .invokeListMethod('select_photo', requestParam);
    } on PlatformException catch (e) {
      throw e;
    }
  }

  // 调用相机选择图片 - 返回空List代表取消
  Future<List<String>?> selectPhotoForCamera() async {
    return selectPhoto(SelectPhotoType.Camera);
  }

  // 调用相册选择图片 - 返回空List代表取消
  Future<List<String>?> selectPhotoForAlbum({int? imageCount}) async {
    return selectPhoto(SelectPhotoType.Album,
        count: imageCount);
  }
}
