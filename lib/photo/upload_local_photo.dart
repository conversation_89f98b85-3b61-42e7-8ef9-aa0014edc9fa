import 'package:flutter/services.dart';

class UploadLocalPhoto {
  MethodChannel _brigdeChannel;

  UploadLocalPhoto.fromChannel(MethodChannel brigdeChannel)
      : _brigdeChannel = brigdeChannel;

  Future<List<String>?> uploadLocalPhoto(
      String uploadUrl, List<String> localPaths,
      {double? limitWidth = 1024,
      double? limitHeight = 1024,
      Map<String, dynamic>? extraParams}) async {
    try {
      Map<String, dynamic> requestParam = {
        'uploadUrl': uploadUrl,
        'localPaths': localPaths,
      };

      if (limitWidth != null && limitWidth > 0) {
        requestParam['limitWidth'] = limitWidth;
      } else {
        requestParam['limitWidth'] = 1024;
      }

      if (limitHeight != null && limitHeight > 0) {
        requestParam['limitHeight'] = limitHeight;
      } else {
        requestParam['limitHeight'] = 1024;
      }

      if (extraParams != null && extraParams.isNotEmpty) {
        requestParam['extraParams'] = extraParams;
      }

      return await this
          ._brigdeChannel
          .invokeListMethod('upload_photo', requestParam);
    } on PlatformException catch (e) {
      throw e;
    }
  }
}
