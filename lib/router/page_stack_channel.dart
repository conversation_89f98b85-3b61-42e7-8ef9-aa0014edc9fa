import 'dart:io';

import 'package:flutter/services.dart';
import 'package:XYYContainer/router/init_route_page.dart';

class PageStackChannel {
  MethodChannel _brigdeChannel;

  PageStackChannel.fromChannel(MethodChannel brigdeChannel)
      : _brigdeChannel = brigdeChannel;

  void setPageStackChange() async {
    if (Platform.isIOS) {
      try {
        await this._brigdeChannel.invokeListMethod(
            'page_stack_change', pageStack.map((e) => e.routeName).toList());
        // ignore: unused_catch_clause
      } on PlatformException catch (ignore) {}
    }
  }
}
