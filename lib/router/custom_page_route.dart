import 'package:flutter/material.dart';

class CustomMaterialPageRoute extends MaterialPageRoute {
  final Duration duration;

  Duration get transitionDuration => duration;

  CustomMaterialPageRoute({
    required WidgetBuilder builder,
    required this.duration,
    RouteSettings? settings,
    bool maintainState = true,
    bool fullscreenDialog = false,
  }) : super(
            builder: builder,
            settings: settings,
            maintainState: maintainState,
            fullscreenDialog: fullscreenDialog);
}
