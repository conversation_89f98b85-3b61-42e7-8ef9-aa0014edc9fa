
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/router/custom_page_route.dart';
import 'package:XYYContainer/router/router_channel.dart';
import 'package:XYYContainer/router/unknown_router_page.dart';
import 'package:flutter/material.dart';

class RouteInfo {
  int routeHashCode;
  String? routeName;

  RouteInfo(this.routeHashCode, this.routeName);
}

final List<RouteInfo> pageStack = <RouteInfo>[];

final GlobalKey<NavigatorState> navigatorKey = new GlobalKey<NavigatorState>();

class CustomObserver extends NavigatorObserver {
  void _removeRoute(Route route) {
    pageStack.removeWhere((element) => element.routeHashCode == route.hashCode);

    // 通知Native端 Flutter页面堆栈变化
    XYYContainer.pageStackChannel.setPageStackChange();
  }

  void _addRoute(Route route) {
    pageStack.add(RouteInfo(route.hashCode, route.settings.name));
    RouterChannel.resultDataToNative = null; // 打开新页面时清空全局返回数据

    // 通知Native端 Flutter页面堆栈变化
    XYYContainer.pageStackChannel.setPageStackChange();
  }

  @override
  void didStopUserGesture() {
    print("CustomObserver,didStopUserGesture");
  }

  @override
  void didStartUserGesture(Route<dynamic> route, Route<dynamic>? previousRoute) {
    String? previousName = '';
    if (previousRoute == null) {
      previousName = 'null';
    } else {
      previousName = previousRoute.settings.name;
    }
    print(
        "CustomObserver,didStartUserGesture，newRoute:${route.settings.name},${route.hashCode}，,oldRoute:$previousName,${previousRoute.hashCode}");
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    String? previousName = '';
    if (oldRoute == null) {
      previousName = 'null';
    } else {
      previousName = oldRoute.settings.name;
    }
    if (pageStack.isNotEmpty) {
      pageStack.removeLast();
    }
    _addRoute(newRoute!);
    print(
        "CustomObserver,didReplace，newRoute:${newRoute.settings.name},${newRoute.hashCode},oldRoute:${previousName},${oldRoute.hashCode}");
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    String? previousName = '';
    if (previousRoute == null) {
      previousName = 'null';
    } else {
      previousName = previousRoute.settings.name;
    }
    _removeRoute(route);
    print(
        "CustomObserver,didRemove，newRoute:${route.settings.name},${route.hashCode},oldRoute:${previousName},${previousRoute.hashCode}");
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    String? previousName = '';
    if (previousRoute == null) {
      previousName = 'null';
    } else {
      previousName = previousRoute.settings.name;
    }
    _removeRoute(route);
    print(
        "CustomObserver,didPop，newRoute:${route.settings.name},${route.hashCode},oldRoute:${previousName},${previousRoute.hashCode}");
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    String? previousName = '';
    if (previousRoute == null) {
      previousName = 'null';
    } else {
      previousName = previousRoute.settings.name;
    }
    _addRoute(route);
    print(
        "CustomObserver,didPush，newRoute:${route.settings.name},${route.hashCode},oldRoute:${previousName},${previousRoute.hashCode}");
  }
}

class InitRoutePage extends StatelessWidget {
  final String initialRoute;

  final CustomMaterialAppBuilder? appBuilder;

  InitRoutePage(this.initialRoute, this.appBuilder);

  PageRoute generatePageRoute(RouteSettings settings) {
    String? routeName;
    dynamic routeParams;
    print('guan:generatePageRoute:${settings.toString()}');
    routeName = settings.name;

    Duration duration;
    if (settings.arguments is EnterAnimDuration) {
      duration = (settings.arguments as EnterAnimDuration).duration;
    } else {
      duration = Duration(milliseconds: 300);
    }


    var parse = Uri.parse(routeName!);

    if (parse.hasQuery) {
      routeName = parse.path;
      routeParams = parse.queryParameters;
    } else if (settings.arguments is! EnterAnimDuration) {
      routeParams = settings.arguments;
    }

    if (!RouterChannel.routes.containsKey(routeName)) {
      routeName = UnknownRouterPage.UNKNOWN_ROUTER;
      routeParams = null;
    }
    // 统一处理
    return CustomMaterialPageRoute(
        builder: (context) {
          return RouterChannel.routes[routeName!]!(context, routeParams);
        },
        duration: duration,
        settings: RouteSettings(name: routeName, arguments: routeParams));
  }

  @override
  Widget build(BuildContext context) {
    pageStack.clear();

    var onGenerateRoute =
        (RouteSettings settings) => generatePageRoute(settings);

    var onGenerateInitialRoutes = (initialRoute) {
      var parse = Uri.parse(initialRoute);
      if (parse.hasQuery) {
        initialRoute.replaceAll('?${parse.query}', '');
      }
      return [
        generatePageRoute(
            RouteSettings(name: parse.path, arguments: parse.queryParameters))
      ];
    };
    if (appBuilder == null) {
      return MaterialApp(
          initialRoute: initialRoute,
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          onGenerateRoute: onGenerateRoute,
          onGenerateInitialRoutes: onGenerateInitialRoutes,
          navigatorObservers: [CustomObserver()]);
    } else {
      return appBuilder!(context, navigatorKey, initialRoute, CustomObserver(),
          onGenerateRoute, onGenerateInitialRoutes);
    }
  }
}
