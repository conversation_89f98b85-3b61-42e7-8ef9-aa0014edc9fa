import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:XYYContainer/router/init_route_page.dart';
import 'package:XYYContainer/router/unknown_router_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class RouterChannel {
  static const BasicMessageChannel<String?> _startUpChannel =
      BasicMessageChannel<String?>('ybb_start_up_channel', StringCodec());

  static const BasicMessageChannel<String?> _routerChannel =
      BasicMessageChannel<String?>('router_channel', StringCodec());

  static const BasicMessageChannel<String?> _removeRouterChannel =
      BasicMessageChannel<String?>('remove_router_channel', StringCodec());

  static const MethodChannel _callbackChannel =
      MethodChannel('callback_channel');

  static HashMap<String, CustomWidgetBuilder> routes =
      HashMap<String, CustomWidgetBuilder>();

  /// 全局保存的路由返回数据,一般是native主动获取
  static Map<String, dynamic>? resultDataToNative;

  static void initRouter(Map<String, CustomWidgetBuilder> routes,
      {String? initialRoute, CustomMaterialAppBuilder? appBuilder}) {
    if (initialRoute == null) {
      //设置默认路径
      initialRoute = window.defaultRouteName;
    }

    routes.forEach((key, value) {
      RouterChannel.routes[key] = value;
    });

    if (!RouterChannel.routes.containsKey(UnknownRouterPage.UNKNOWN_ROUTER)) {
      RouterChannel.routes[UnknownRouterPage.UNKNOWN_ROUTER] =
          (context, arguments) => UnknownRouterPage();
    }

    print(
        'guan:RouterChannel.routes:${RouterChannel.routes.runtimeType},routes:${routes.runtimeType}');

    print(
        'guan:defaultRoute:${window.defaultRouteName}，initialRoute:${initialRoute}');

    //设置rootwidget
    runApp(InitRoutePage(initialRoute, appBuilder));

    // 初始化路由
    initStartUpChannel(appBuilder);

    //通用路由
    initRouterChannel();

    // 回传值路由
    initCallbackChannel();

    // 移除路由初始化
    initRemoveChannel();
  }

  static void initRouterChannel() {
    _routerChannel.setMessageHandler((url) async {
      print('guan:router_channel:$url');
      var resultData;
      try {
        var parse = Uri.parse(url!);
        print('guan:open internal path:${parse.path},query:${parse.query}');
        var currentContext = navigatorKey.currentContext;
        if (currentContext != null) {
          print('guan:has currentContext path:${parse.path},query:${parse.query}');
          var tempResultData = await Navigator.of(currentContext)
              .pushNamed(parse.path, arguments: parse.queryParameters);
          print('guan:has tempResultData path:${tempResultData}');
          if (tempResultData is Map) {
            resultData = json.encode(tempResultData);
          } else {
            resultData = tempResultData;
          }
          print('guan routerchannel resultData:${resultData}');
        } else {
          print('guan:globalcontext is null');
        }
      } on Exception catch (e) {
        print('guan:error:$e');
      }
      return resultData;
    });
  }

  static void initStartUpChannel(CustomMaterialAppBuilder? appBuilder) {
    _startUpChannel.setMessageHandler((initialRouterName) async {
      print('guan:ybb_start_up_channel:$initialRouterName');
      if (Platform.isAndroid) {
        //Android
        var currentContext = navigatorKey.currentContext;
        if (currentContext != null) {
          Navigator.of(currentContext).pushReplacementNamed(initialRouterName!,
              arguments: EnterAnimDuration(Duration(milliseconds: 0)));
        }
      }
      return Future.value('true');
    });
  }

  static void initRemoveChannel() {
    _removeRouterChannel.setMessageHandler((message) async {
      print('feng:remove_router_channel');
      if (Platform.isIOS) {
        var currentContext = navigatorKey.currentContext;
        if (currentContext != null) {
          Navigator.of(currentContext)
              .pushNamedAndRemoveUntil('/', (route) => false);
          print('feng:remove_router_channel_end');
        }
      }
      // 移除图片内存缓存
      PaintingBinding.instance?.imageCache?.clear();
      return Future.value('true');
    });
  }

  static void initCallbackChannel() {
    _callbackChannel.setMethodCallHandler((call) async {
      return resultDataToNative;
    });
  }

  static void open(String url, {OpenCallback? callback}) {
    var sendResultFuture = _routerChannel.send(url);
    if (callback != null) {
      sendResultFuture.then((value) {
        if (callback != null) {
          Map<String, dynamic>? resultData;
          try {
            var decode = json.decode(value!);
            if (decode is Map<String, dynamic>) {
              resultData = decode;
            }
          } catch (e) {
            print("guan:opencallback error:${e}");
          }
          callback(resultData);
        }
      });
    }
  }

  static void setResult(Map<String, dynamic>? resultData) {
    resultDataToNative = resultData;
  }
}

typedef OpenCallback = void Function(Map<String, dynamic>? resultData);

class EnterAnimDuration {
  final Duration duration;

  EnterAnimDuration(this.duration);
}

typedef CustomWidgetBuilder = Widget Function(
    BuildContext context, Map<String, dynamic>? arguments);

typedef CustomMaterialAppBuilder = MaterialApp Function(
    BuildContext context,
    GlobalKey<NavigatorState> navigatorKey,
    String initialRouter,
    NavigatorObserver navigatorObserver,
    RouteFactory onGenerateRoute,
    InitialRouteListFactory onGenerateInitialRoutes);
