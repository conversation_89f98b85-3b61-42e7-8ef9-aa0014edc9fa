import 'dart:async';

import 'package:XYYContainer/router/page_stack_channel.dart';
import 'package:XYYContainer/router/router_channel.dart';
import 'package:XYYContainer/storage/storage_channel.dart';
import 'package:XYYContainer/toast/toast_channel.dart';
import 'package:XYYContainer/tools/default_bridge_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'location/location_channel.dart';
import 'native_router_channel/native_router_channel.dart';
import 'photo/upload_local_photo.dart';
import 'request_channel/request_channel.dart';
import 'photo/select_photo_channel.dart';

import 'package:flutter/services.dart';
import 'dart:io';

class XYYContainer {
  static const MethodChannel _xyyxxBridgeChannel =
      const MethodChannel('XYYXX_bridge');

  // 自定义桥处理类的缓存
  static Map<String, BridgeBaseHandler> _registerBridge = Map();

  static void init(Map<String, CustomWidgetBuilder> routes,
      {String? initialRoute, CustomMaterialAppBuilder? appBuilder}) {
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.dumpErrorToConsole(details);
      Zone.current.handleUncaughtError(details.exception,
          details.stack ?? StackTrace.fromString("stack is null"));
    };
    runZonedGuarded<Future<Null>>(() async {
      RouterChannel.initRouter(routes,
          initialRoute: initialRoute, appBuilder: appBuilder);
    }, (error, stackTrace) async {
      _reportToErrorNative("$error,$stackTrace");
    });
    _xyyxxBridgeChannel.setMethodCallHandler((call) => _handleMethodCall(call));
  }

  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    if (_registerBridge.keys.contains(call.method)) {
      return _registerBridge[call.method]!
          .handleMethodCall(call);
    }
  }

  // 自定义桥注册
  static void registerBridge(String methodName,
      {BridgeBaseHandler? bridgeHandler}) {
    if (bridgeHandler == null) {
      bridgeHandler = DefaultBridgeHandler(methodName);
    }
    _registerBridge[methodName] = bridgeHandler;
  }

  // 自定义桥的调用，flutter to native
  static Future bridgeCall(String methodName,
      {Map<String, dynamic>? parameters}) async {
    if (_registerBridge.keys.contains(methodName)) {
      return _registerBridge[methodName]!
          .handler(parameters, _xyyxxBridgeChannel);
    }
  }

  // 网络请求桥的处理
  static RequestChannel requestChannel =
      RequestChannel.fromChannel(_xyyxxBridgeChannel);

  // 相机、相册图片选择的桥
  static SelectPhotoChannel selectPhotoChannel =
      SelectPhotoChannel.fromChannel(_xyyxxBridgeChannel);

  // 本地图片上传的桥
  static UploadLocalPhoto uploadLocalPhoto =
      UploadLocalPhoto.fromChannel(_xyyxxBridgeChannel);

  //toast的桥
  static ToastChannel toastChannel =
      ToastChannel.fromChannel(_xyyxxBridgeChannel);

  //存储的桥
  static StorageChannel storageChannel =
      StorageChannel.fromChannel(_xyyxxBridgeChannel);

  //定位的桥
  static LocationChannel locationChannel =
      LocationChannel.fromChannel(_xyyxxBridgeChannel);

  // iOS退出容器的桥 - 仅在iOS平台调用生效
  static NativeRouterChannel nativeRouterChannel =
      NativeRouterChannel.fromChannel(_xyyxxBridgeChannel);

  // 页面栈变化通知桥
  static PageStackChannel pageStackChannel =
      PageStackChannel.fromChannel(_xyyxxBridgeChannel);

  // 调用相机选择图片并上传 - 返回空List代表取消
  static Future<List<String>?> photoForCameraWithUpload(String uploadUrl,
      {double? limitWidth,
      double? limitHeight,
      Map<String, dynamic>? extraParams}) async {
    try {
      List<String>? localPaths =
          await selectPhotoChannel.selectPhotoForCamera();
      if (localPaths == null || localPaths.isEmpty) {
        return [];
      }
      return uploadLocalPhoto.uploadLocalPhoto(uploadUrl, localPaths,
          limitWidth: limitWidth,
          limitHeight: limitHeight,
          extraParams: extraParams);
    } on PlatformException catch (e) {
      throw e;
    }
  }

  // 调用相册选择图片并上传 - 返回空List代表取消
  static Future<List<String>?> photoForAlbumWithUpload(String uploadUrl,
      {int? imageCount,
      double? limitWidth,
      double? limitHeight,
      Map<String, dynamic>? extraParams}) async {
    try {
      List<String>? localPaths =
          await selectPhotoChannel.selectPhotoForAlbum(imageCount: imageCount);
      if (localPaths == null || localPaths.isEmpty) {
        return [];
      }
      return uploadLocalPhoto.uploadLocalPhoto(uploadUrl, localPaths,
          limitWidth: limitWidth,
          limitHeight: limitHeight,
          extraParams: extraParams);
    } on PlatformException catch (e) {
      throw e;
    }
  }

  static void open(String url, {OpenCallback? callback}) {
    RouterChannel.open(url, callback: callback);
  }

  static void close(BuildContext context, {Map<String, dynamic>? resultData}) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).maybePop(resultData);
    } else {
      setResult(resultData);
      if (Platform.isIOS) {
        nativeRouterChannel.pop(animated: true);
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }

  static void setResult(Map<String, dynamic>? resultData) {
    RouterChannel.setResult(resultData);
  }

  static void _reportToErrorNative(String errorDetail) {
    _xyyxxBridgeChannel
        .invokeMethod('report_error', {"errorDetail": errorDetail});
  }
}

// 自定义桥的基类
abstract class BridgeBaseHandler {
  const BridgeBaseHandler({this.methodName});

  final String? methodName;

  Map<String, dynamic>? prepareParameters(Map<String, dynamic>? parameters) {
    return parameters;
  }

  Future handler(
      Map<String, dynamic>? params, MethodChannel _xyyxxBridgeChannel) async {
    var resultParams = prepareParameters(params);
    return await _xyyxxBridgeChannel.invokeMethod(
        this.methodName!, resultParams);
  }

  Future<dynamic> handleMethodCall(MethodCall call) async {
    return null;
  }
}
