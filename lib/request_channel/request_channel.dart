import 'package:flutter/services.dart';
import 'dart:convert' as convert;
import '../tools/tools.dart';

/// 请求方式
enum RequestMethod { GET, POST }

/// 请求响应类型
enum RequestContentType { FORM, JSON }

class RequestChannel {
  MethodChannel _brigdeChannel;

  RequestChannel.fromChannel(MethodChannel brigdeChannel)
      : _brigdeChannel = brigdeChannel;

  Future request(String path,
      {RequestMethod? method,
      RequestContentType? contentType,
      Map<String, String>? additionalHeader,
      dynamic parameters}) async {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam["path"] = path;
      if (method != null) {
        requestParam["method"] = enumToString(method);
      }
      if (contentType != null) {
        requestParam["content_type"] = enumToString(contentType);
      }
      if (additionalHeader != null) {
        requestParam["header"] = additionalHeader;
      }
      if (parameters != null) {
        requestParam["parameters"] = parameters;
      }
      return await this._brigdeChannel.invokeMethod('network', requestParam);
    } on PlatformException catch (e) {
      throw e;
    }
  }
}

class ResponseResult {
  ResponseError? error;

  bool? isSuccess;

  var responseObject;

  String? status;
  String? message;
  var data;

  ResponseResult.fromError(PlatformException e) {
    ResponseError error = ResponseError(code: e.code, message: e.message);
    this.error = error;
  }

  static ResponseResult fromJsonStr(String jsonStr) {
    Map<dynamic, dynamic> json = convert.jsonDecode(jsonStr);
    return ResponseResult.fromJson(json);
  }

  ResponseResult.fromJson(Map<dynamic, dynamic> json)
      : status = json['status'],
        isSuccess = (json['status'] == 'success'),
        message = json['message'],
        data = json['data'],
        responseObject = json;
}

class ResponseError extends Error {
  String? code;
  String? message;
  String? detail;

  ResponseError({this.code, this.message, this.detail}) : super();
}
