import 'package:flutter/services.dart';
import '../tools/tools.dart';

/// Toast类型
enum ToastType { Normal, Success, Error }

class ToastChannel {
  MethodChannel _bridgeChannel;

  ToastChannel.fromChannel(MethodChannel bridgeChannel)
      : _bridgeChannel = bridgeChannel;

  void toast(String msg, {ToastType type = ToastType.Normal}) {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['type'] = enumToString(type);

      requestParam['msg'] = msg;

      this._bridgeChannel.invokeMapMethod('toast', requestParam);
    // ignore: unused_catch_clause
    } on PlatformException catch (ignore) {}
  }
}
