import 'package:flutter/services.dart';

import 'location_data.dart';

class LocationChannel {
  MethodChannel _bridgeChannel;

  LocationChannel.fromChannel(MethodChannel bridgeChannel)
      : _bridgeChannel = bridgeChannel;

  Future<LocationData> locate() async {
    try {
      var result = await _bridgeChannel.invokeMapMethod('location');
      return LocationData(
          isSuccess: true,
          latitude: result!["latitude"]?.toString() ?? "",
          longitude: result["longitude"]?.toString() ?? "",
          address: result["address"]);
    } catch (e) {
      return LocationData(isSuccess: false);
    }
  }
}
