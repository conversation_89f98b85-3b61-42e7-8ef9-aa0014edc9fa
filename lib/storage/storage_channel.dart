import 'package:flutter/services.dart';

class StorageChannel {
  MethodChannel _bridgeChannel;

  StorageChannel.fromChannel(MethodChannel bridgeChannel)
      : _bridgeChannel = bridgeChannel;

  // 数据存储
  // data: 参数
  // space: 分区 (默认default)
  Future<bool?> putAll(Map<String, dynamic> data,
      {String space = 'default'}) async {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['data'] = data;
      requestParam['space'] = space;
      requestParam['type'] = 'putAll';

      return this._bridgeChannel.invokeMethod('storage', requestParam);
      // ignore: unused_catch_clause
    } on PlatformException catch (e) {
      throw e;
    }
  }

  // 数据存储
  // data: 参数
  // space: 分区 (默认default)
  Future<bool?> put(String key, dynamic value,
      {String space = 'default'}) async {
    return this.putAll({key: value}, space: space);
  }

  // 数据获取
  // 获取 space 区下的全部数据
  Future<Map<String, dynamic>?> getAll({String space = 'default'}) async {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['space'] = space;
      requestParam['type'] = 'getAll';

      return this._bridgeChannel.invokeMapMethod('storage', requestParam);
      // ignore: unused_catch_clause
    } on PlatformException catch (e) {
      throw e;
    }
  }

  // 获取 space 下 key 对应存储的数据
  Future<dynamic> getValue(String key, {String space = 'default'}) async {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['space'] = space;
      requestParam['key'] = key;
      requestParam['type'] = 'getValue';

      return this._bridgeChannel.invokeMethod('storage', requestParam);
      // ignore: unused_catch_clause
    } on PlatformException catch (e) {
      throw e;
    }
  }

  // 删除 space 中 key 对应的存储
  Future<bool?> delete(String key, {String space = 'default'}) async {
    return this.put(key, null, space: space);
  }

  // 删除 space 中 key 对应的存储
  Future<bool?> deleteAll(String space) async {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['space'] = space;
      requestParam['type'] = 'deleteAll';

      return this._bridgeChannel.invokeMethod('storage', requestParam);
      // ignore: unused_catch_clause
    } on PlatformException catch (e) {
      throw e;
    }
  }
}
