

import 'package:flutter/services.dart';

class NativeRouterChannel {
  MethodChannel _bridgeChannel;

  NativeRouterChannel.fromChannel(MethodChannel brigdeChannel)
      : _bridgeChannel = brigdeChannel;

  void pop({bool animated = true}) {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['animated'] = animated;

      this._bridgeChannel.invokeMapMethod('pop', requestParam);
      // ignore: unused_catch_clause
    } on PlatformException catch (ignore) {}
  }

  void dismiss({bool animated = true}) {
    try {
      Map<String, dynamic> requestParam = {};
      requestParam['animated'] = animated;
      this._bridgeChannel.invokeMapMethod('dismiss', requestParam);
      // ignore: unused_catch_clause
    } on PlatformException catch (ignore) {}
  }

  Future<bool> canDismiss() async {
    try {
      var result = await this._bridgeChannel.invokeMethod('canDismiss');
      return result ?? false;
      // ignore: unused_catch_clause
    } on PlatformException catch (ignore) {
      return false;
    }
  }
}
