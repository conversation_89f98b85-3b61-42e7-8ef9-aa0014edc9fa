//
//  XYYXXBridgeRegister.h
//  XYYContainer
//
//  Created by FW on 2020/10/22.
//

#import <Foundation/Foundation.h>
#import "XYYXXBridge.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, XYYXXToastType) {
    XYYXXToastTypeNormal,
    XYYXXToastTypeSuccess,
    XYYXXToastTypeError,
};

@protocol XYYXXBridgeInterface <NSObject>

@required
/// 网络请求
/// @param path 请求的路径
/// @param method 请求方式 POST或GET 字符串类型
/// @param contentType 响应类型 字符串
/// @param parameters 请求参数
/// @param addHeader 添加的请求头信息
/// @param handler 请求完成后的回调
- (void)requestWithPath:(NSString * _Nonnull)path
             withMethod:(NSString * _Nullable)method
        withContentType:(NSString *_Nullable)contentType
         withParameters:(id _Nullable)parameters
            withHeaders:(NSDictionary<NSString *, NSString*> * _Nullable)addHeader
             andHandler:(void(^)(NSString * _Nullable responseJSONStr, NSError * _Nullable error))handler;

/// 图片上传
/// @param path 上传的URL地址
/// @param imageData 图片的二进制文件
/// @param handler 上传成功后的回调
- (void)uploadImageWithPath:(NSString * _Nonnull)path
              withImageData:(NSData * _Nonnull)imageData
            withExtraParams:(NSDictionary * _Nullable)extraParams
                withHandler:(void(^)(NSString * _Nullable imagerUrl, NSError * _Nullable error))handler;

/// Toast提示
/// @param message 提示信息
/// @param toastType 提示类型
- (void)showToastWithMessage:(NSString *)message withType:(XYYXXToastType)toastType;

/// 请求定位
/// @param handler 定位成功后的回调
- (void)requestSingleLocationWithHandler:(void(^)(NSString * _Nullable address, double latitude, double longitude, NSError * _Nullable error))handler;

@optional
/// Flutter异常信息等回调
/// @param errorInfo 异常信息
- (void)reportErrorWithInfo:(NSString *)errorInfo;

@end


@interface XYYXXBridgeRegistry : NSObject

@property (nonatomic, class, strong) id <XYYXXBridgeInterface> _Nullable bridgeInterface;

+ (XYYXXBridge * _Nullable)bridgeWithMethodName:(NSString *)methodName;

@end

NS_ASSUME_NONNULL_END
