//
//  XYYXXBridgeRegister.m
//  XYYContainer
//
//  Created by FW on 2020/10/22.
//

#import "XYYXXBridgeRegistry.h"
#import "XYYXXRuntime.h"
#import "XYYXXBridge.h"

static NSMutableDictionary<NSString *, Class> *_registries;
static BOOL _registrationFinished = NO;

static id <XYYXXBridgeInterface> _Nullable _bridgeInterface;

@interface XYYXXBridgeRegistry()

@property (nonatomic, class, readonly) NSMutableDictionary * registries;

@property (nonatomic, class) BOOL registrationFinished;

@end

@implementation XYYXXBridgeRegistry

/// 自动注册处理
+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _registries = [NSMutableDictionary dictionary];
        xyyxx_replaceMethodWithMethod([UIApplication class], @selector(setDelegate:), self, @selector(xyyxx_BridgeRegistry_hook_setDelegate:));
    });
}

+ (void)setBridgeInterface:(id<XYYXXBridgeInterface>)bridgeInterface {
    _bridgeInterface = bridgeInterface;
}

+ (id<XYYXXBridgeInterface>)bridgeInterface {
    return _bridgeInterface;
}

+ (void)xyyxx_BridgeRegistry_hook_setDelegate:(id)delegate {
    [XYYXXBridgeRegistry registryAllBridge];
    [self xyyxx_BridgeRegistry_hook_setDelegate: delegate];
}

+ (BOOL)registrationFinished {
    return _registrationFinished;
}

+ (NSDictionary<NSString *, Class> *)registries {
    return _registries;
}

+ (void)setRegistrationFinished:(BOOL)registrationFinished {
    _registrationFinished = registrationFinished;
}

+ (void)registryAllBridge {
    if (self.registrationFinished) {
        return ;
    }
    if (xyyxx_canEnumerateClassesInImage()) {
        /// 快速遍历类
        xyyxx_enumerateClassesInMainBundleForParentClass([XYYXXBridge class], ^(__unsafe_unretained Class  _Nonnull aClass) {
            [self handleEnumerateBridgeClass: aClass];
        });
    } else {
        /// 遍历全部类 - 慢
        xyyxx_enumerateClassList(^(Class  _Nonnull __unsafe_unretained aClass) {
            [self handleEnumerateBridgeClass: aClass];
        });
    }
    self.registrationFinished = YES;
    [self notifiyRegistrationFinished];
}

+ (void)handleEnumerateBridgeClass:(Class)class {
    static Class XYYXXBridgeClass;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        XYYXXBridgeClass = [XYYXXBridge class];
    });
    if (xyyxx_classIsSubclassOfClass(class, XYYXXBridgeClass)) {
        NSMutableDictionary * registryMap = [[self registries] mutableCopy];
        NSString * methodName = [class bridgeMethodName];
        [registryMap setObject:class forKey:methodName];
        _registries = registryMap;
    }
#if DEBUG
    /// 回头可以把映射关系写到文件中，或者打印出来，方便查找
#endif
}


+ (void)notifiyRegistrationFinished {
    NSDictionary * registryMap = [[self registries] copy];
    for (Class Bridge in registryMap.allValues) {
        [Bridge didFinishRegistration];
    }
}


#pragma mark - 调用逻辑
+ (XYYXXBridge * _Nullable)bridgeWithMethodName:(NSString *)methodName {
    NSDictionary * registryMap = [[self registries] copy];
    Class bridgeClass = [registryMap objectForKey:methodName];
    if (bridgeClass != nil) {
        return [bridgeClass bridgeDestination];
    }
    return nil;
}

@end
