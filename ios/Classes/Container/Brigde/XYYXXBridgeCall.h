//
//  XYYXXBridgeCall.h
//  XYYContainer
//
//  Created by FW on 2020/10/28.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>


@class XYYXXFlutterEngine, XYYXXBridge;

NS_ASSUME_NONNULL_BEGIN

@interface XYYXXBridgeCall : NSObject

+ (instancetype)BridgeCallWithEngine:(FlutterEngine * _Nullable)engine andBridge:(XYYXXBridge *)bridge andFlutterCall:(FlutterMethodCall *)call andFlutterResult:(FlutterResult)flutterResult;

@property (nonatomic, readonly, strong) FlutterEngine * _Nullable engine;

@property (nonatomic, readonly, strong) FlutterMethodCall * call;

@property (nonatomic, readonly, copy) void (^bridgeResult)(id _Nullable result);

@end

NS_ASSUME_NONNULL_END
