//
//  XYYXXBridgeCall.m
//  XYYContainer
//
//  Created by FW on 2020/10/28.
//

#if __has_include(<XYYContainer/XYYContainer-Swift.h>)
#import <XYYContainer/XYYContainer-Swift.h>
#else
#import "XYYContainer-Swift.h"
#endif

#import "XYYXXBridgeCall.h"
#import "XYYXXBridge.h"
#import "XYYXXFlutterViewController.h"

@interface XYYXXBridgeCall()

@property (nonatomic, strong) FlutterEngine * _Nullable engine;

@property (nonatomic, strong) FlutterMethodCall * call;

@property (nonatomic, strong) XYYXXBridge * bridge;

@property (nonatomic, copy) void (^bridgeResult)(id _Nullable result);

@end

@implementation XYYXXBridgeCall

+ (instancetype)BridgeCallWithEngine:(FlutterEngine * _Nullable)engine andBridge:(XYYXXBridge *)bridge andFlutterCall:(FlutterMethodCall *)call andFlutterResult:(FlutterResult)flutterResult {
    XYYXXBridgeCall * bridgeCall = [[XYYXXBridgeCall alloc] init];
    bridgeCall.engine = engine;
    bridgeCall.call = call;
    bridgeCall.bridge = bridge;
    __weak typeof(XYYXXBridgeCall *) weakBridgeCall = bridgeCall;
    bridgeCall.bridgeResult = ^(id result){
        if ([weakBridgeCall.engine.viewController isKindOfClass:[XYYXXFlutterViewController class]]) {
            XYYXXFlutterViewController * flutterController = (XYYXXFlutterViewController *)weakBridgeCall.engine.viewController;
            NSMutableArray * bridgeArray = [flutterController.bridgeSets mutableCopy];
            if ([bridgeArray containsObject:weakBridgeCall.bridge]) {
                [bridgeArray removeObject:weakBridgeCall.bridge];
                flutterController.bridgeSets = bridgeArray;
            }
        }
        flutterResult(result);
    };
    return bridgeCall;
}

@end
