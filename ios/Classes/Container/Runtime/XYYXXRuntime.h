//
//  XYYXXRuntime.h
//  XYYContainer
//
//  Created by FW on 2020/10/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXTERN bool xyyxx_replaceMethodWithMethod(Class originalClass, SEL originalSelector,
                                                   Class swizzledClass, SEL swizzledSelector);

FOUNDATION_EXTERN bool xyyxx_classIsSubclassOfClass(Class aClass, Class parentClass);

FOUNDATION_EXTERN void xyyxx_enumerateClassList(void(^handler)(Class aClass));

FOUNDATION_EXTERN BOOL xyyxx_canEnumerateClassesInImage(void);

FOUNDATION_EXTERN void xyyxx_enumerateClassesInMainBundleForParentClass(Class parentClass, void(^handler)(__unsafe_unretained Class aClass));

NS_ASSUME_NONNULL_END
