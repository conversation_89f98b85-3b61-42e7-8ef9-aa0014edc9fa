//
//  XYYXXRouterManager.swift
//  XYYContainer
//
//  Created by FW on 2020/12/15.
//

import Foundation
import Flutter

public typealias XYYXXOpenNewFlutterPage = (_ initalRouter: String, _ replay: XYYXXRouterCallback?) -> Void

public typealias XYYXXOpenRouterPage = (_ router: String, _ replay: XYYXXRouterCallback?) -> Void

public typealias XYYXXRouterCallback = (_ replyMessage: Any?) -> Void

@objc(XYYXXRouterManager)
open class XYYXXRouterManager: NSObject {
    
    static let share: XYYXXRouterManager = XYYXXRouterManager()
    
    private var validations: [XYYXXRouterValidation] = [XYYXXRouterCheckFlutterValidation()]
    
    fileprivate var openNativePage: XYYXXOpenRouterPage?
    
    fileprivate var openNewFlutterPage: XYYXXOpenNewFlutterPage?
    
    /// 是否使用默认
    @objc public static var defaultValidation: Bool = true {
        didSet {
            if !defaultValidation {
                XYYXXRouterManager.removeValidation(for: "XYYXXRouterCheckFlutterValidation")
            }
        }
    }
    
    /// 设置打开新Flutter页面方法
    /// - Parameter handler: 打开新Flutter页面
    @objc public static func setOpenNewFlutterPage(_ handler: @escaping XYYXXOpenNewFlutterPage) {
        XYYXXRouterManager.share.openNewFlutterPage = handler
    }
    
    /// 设置打开Native页面方法
    /// - Parameter handler: 打开Native页面方法
    @objc public static func setOpenNativePage(_ handler: @escaping XYYXXOpenRouterPage) {
        XYYXXRouterManager.share.openNativePage = handler
    }
    
    /// 添加验证规则 - 所有路由跳转都会进行验证，验证顺序由添加顺序决定， 当遇到返回路径为空时结束验证
    /// - Parameter validationHandler: 验证实例
    @objc public static func addValidation(validationHandler: XYYXXRouterValidation) {
        XYYXXRouterManager.share.validations.append(validationHandler)
    }
    
    /// 移除验证规则
    /// - Parameter key: 验证规则对应的key值
    @objc public static func removeValidation(for key: String) {
        XYYXXRouterManager.share.validations.removeAll(where: { $0.validationKey == key })
    }
    
    /// 打开页面
    /// - Parameter router: 路由协议
    @objc public static func openPage(for router: String, reply: XYYXXRouterCallback? = nil) {
        var validationRouter: String? = router
        for validation in XYYXXRouterManager.share.validations {
            if validationRouter != nil {
                validationRouter = validation.validateRouter(validationRouter!, reply: reply)
            }
        }
        if let targetRouter = validationRouter, targetRouter.count > 0 {
            let topController = XYYXXRouterManager.share.stackTopController()
            if let flutterController = topController as? XYYXXFlutterViewController {
                /// 如果栈顶是FlutterController 则直接调用channel打开新的Flutter页面， 并处理回调传值
                flutterController.engine?.routerChannel?.sendMessage(targetRouter, reply: { (backMessage) in
                    reply?(backMessage)
                })
            } else {
                XYYXXRouterManager.share.openNewFlutterPage?(targetRouter, reply)
            }
        }
    }
    
    fileprivate func stackTopController() -> UIViewController? {
        var viewController = UIApplication.shared.delegate?.window??.rootViewController
        while true {
            if viewController is UITabBarController {
                viewController = (viewController as? UITabBarController)?.selectedViewController
            }
            if viewController is UINavigationController {
                viewController = (viewController as? UINavigationController)?.topViewController
            }
            if viewController?.presentedViewController != nil {
                viewController = viewController?.presentedViewController
            } else {
                break
            }
        }
        return viewController
    }
}


@objc(XYYXXRouterValidation)
public protocol XYYXXRouterValidation: NSObjectProtocol {
    
    @objc var validationKey: String { get }
    
    @objc func validateRouter(_ router: String, reply: XYYXXRouterCallback?) -> String?
    
}

@objc(XYYXXRouterCheckFlutterValidation)
public class XYYXXRouterCheckFlutterValidation: NSObject, XYYXXRouterValidation {
    public func validateRouter(_ router: String, reply: XYYXXRouterCallback?) -> String? {
        guard let components = URLComponents(string: router) else { return nil }
        if let host = components.host, let scheme = components.scheme {
            if host.count > 0 {
                let path = components.path
                if path.hasPrefix("/flutter") {
                    // 如果是以Flutter为path开头 则处理跳转flutter
                    let targerRouter = router.replacingOccurrences(of: "\(scheme)://\(host)/flutter", with: "")
                    return targerRouter
                }
                let targerRouter = router.replacingOccurrences(of: scheme + "://" + host, with: "")
                XYYXXRouterManager.share.openNativePage?(targerRouter, { (replayMessage) in
                    reply?(replayMessage)
                })
                return nil
            }
        }
        return router
    }
    
    public var validationKey: String {
        return NSStringFromClass(self.classForCoder)
    }
    
}

