//
//  CRMFlutterEnginePool.swift
//  CRM
//
//  Created by FW on 2020/9/7.
//  Copyright © 2020 Xiaoyaoyao. All rights reserved.
//

import UIKit

let XYYXXFlutterEngineNamePerfix: String = "XYYXXFlutter_Cache_Engine_"

@objc(XYYXXFlutterEnginePool)
public class XYYXXFlutterEnginePool: NSObject {
    
    @objc public static let share: XYYXXFlutterEnginePool = XYYXXFlutterEnginePool()
    
    /// 当前最多缓存的引擎数量
    private let cacheFlutterEngineCount: Int = 3
    
    private var allEngineIds: [String] = []
    
    private var userEngineIds: [String] = []
    
    /// 缓存
    private lazy var cacheEngines: NSCache<NSString, FlutterEngine> = {
        let cacheEngines = NSCache<NSString, FlutterEngine>()
        cacheEngines.countLimit = self.cacheFlutterEngineCount
        return cacheEngines
    }()
    
    @objc public func setCacheEngine(with engine: FlutterEngine) {
        let cacheKey = String(format: "%p", engine)
        self.cacheEngines.setObject(engine, forKey: cacheKey as NSString)
        self.userEngineIds.append(cacheKey)
        self.allEngineIds.append(cacheKey)
    }
    
    @objc public func getUnUseCacheEngine() -> FlutterEngine? {
        let cacheKey = self.allEngineIds.first(where: { !self.userEngineIds.contains($0) })
        if let key = cacheKey {
            let engine = self.cacheEngines.object(forKey: key as NSString)
            self.userEngineIds.append(key)
            return engine
        }
        return nil
    }
    
    /// 移除当前正在使用的引擎
    @objc public func removeEngine(for engine: FlutterEngine) {
        let cacheKey = String(format: "%p", engine)
        self.userEngineIds.removeAll(where: { $0 == cacheKey })
        if self.allEngineIds.count > 3 {
            DispatchQueue.main.async {
                if let engine = self.cacheEngines.object(forKey: cacheKey as NSString) {
//                    engine.destroyContext()
                    self.cacheEngines.removeObject(forKey: cacheKey as NSString)
                    self.allEngineIds.removeAll(where: { $0 == cacheKey })
                }
            }
        }
    }
}
