//
//  XYYFlutterEngine.swift
//  CRM
//
//  Created by FW on 2020/9/7.
//  Copyright © 2020 Xiaoyaoyao. All rights reserved.
//

import UIKit

extension FlutterEngine {
    private struct OverKey {
        static let engineChannelKey = UnsafeRawPointer(bitPattern: "Container_EngineChannel_Key".hashValue)!
        static let routerChannelKey = UnsafeRawPointer(bitPattern: "Container_routerChannelKey_Key".hashValue)!
        static let callBackChannelKey = UnsafeRawPointer(bitPattern: "Container_callBackChannelKey_Key".hashValue)!
        static let pageStackListKey = UnsafeRawPointer(bitPattern: "Container_PageStackList_Key".hashValue)!
        static let registrarsKey = UnsafeRawPointer(bitPattern: "Container_Registrars_Key".hashValue)!
        static let removeRouterChannelKey = UnsafeRawPointer(bitPattern: "Container_removeRouter<PERSON>hannelKey_Key".hashValue)!
    }
    
    @objc public var cacheRegistrars: [String: FlutterPluginRegistrar] {
        set {
            objc_setAssociatedObject(self, OverKey.registrarsKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, OverKey.registrarsKey) as? [String: FlutterPluginRegistrar] ?? [:]
        }
    }

    @objc public var pageStackList: [String] {
        set {
            objc_setAssociatedObject(self, OverKey.pageStackListKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, OverKey.pageStackListKey) as? [String] ?? []
        }
    }
    
    var bridgeChannel: FlutterMethodChannel? {
        set {
            objc_setAssociatedObject(self, OverKey.engineChannelKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, OverKey.engineChannelKey) as? FlutterMethodChannel
        }
    }
    
    var routerChannel: FlutterBasicMessageChannel? {
        set {
            objc_setAssociatedObject(self, OverKey.routerChannelKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, OverKey.routerChannelKey) as? FlutterBasicMessageChannel
        }
    }
    
    var removeRouterChannel: FlutterBasicMessageChannel? {
        set {
            objc_setAssociatedObject(self, OverKey.removeRouterChannelKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, OverKey.removeRouterChannelKey) as? FlutterBasicMessageChannel
        }
    }
    
    @objc public var callBackChannel: FlutterMethodChannel? {
        set {
            objc_setAssociatedObject(self, OverKey.callBackChannelKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, OverKey.callBackChannelKey) as? FlutterMethodChannel
        }
    }
    
    @objc public func removeRouter() {
        self.removeRouterChannel?.sendMessage(nil);
    }
    
    @objc public func registerChannel() {
        
        /// 页面回调桥
        self.callBackChannel = FlutterMethodChannel(name: "callback_channel", binaryMessenger: self.binaryMessenger)
        
        /// 路由桥
        self.routerChannel = FlutterBasicMessageChannel(name: "router_channel", binaryMessenger: self.binaryMessenger,  codec: FlutterStringCodec.sharedInstance())
        
        /// 移除路由桥
        self.removeRouterChannel = FlutterBasicMessageChannel(name: "remove_router_channel", binaryMessenger: self.binaryMessenger,  codec: FlutterStringCodec.sharedInstance())
        
        self.routerChannel?.setMessageHandler { (message, replay) in
            
            guard let router = message as? String else {
                let error = FlutterError(code: "-1", message: "路由错误，请检查路由入参!", details: nil)
                replay(error)
                return
            }
            
            XYYXXRouterManager.openPage(for: router) { (result) in
                replay(result)
            }
        }
        
        /// 通讯桥
        self.bridgeChannel = FlutterMethodChannel(name: "XYYXX_bridge", binaryMessenger: self.binaryMessenger)
        
        self.bridgeChannel?.setMethodCallHandler { [weak self] (call, result) in
            /// 根据调用方法名称，生成对应的桥实例
            if let bridgeInstance = XYYXXBridgeRegistry.bridge(withMethodName: call.method) {
                /// 缓存桥的实例，防止因执行异步任务桥被提前释放
                if let controller = self?.viewController as? XYYXXFlutterViewController {
                    var bridgetSets: [XYYXXBridge] = []
                    let controllerBridgeSts: [XYYXXBridge]? = controller.bridgeSets as? [XYYXXBridge]
                    bridgetSets.append(contentsOf: controllerBridgeSts ?? [])
                    if !bridgetSets.contains(bridgeInstance) {
                        bridgetSets.append(bridgeInstance)
                    }
                    if let bridgets = bridgetSets as? NSMutableArray {
                        controller.bridgeSets = bridgets
                    }
                    
                }
                let bridgeCall = XYYXXBridgeCall(engine: self, andBridge: bridgeInstance, andFlutterCall: call, andFlutterResult: result)
                bridgeInstance.bridgeMessage(with: bridgeCall)
            }
        }
        
        /// 复用引擎的二次插件注册
        self.cacheRegistrars.forEach { (pluginKey, registrar) in
            NSClassFromString(pluginKey)?.register(with: registrar)
        }
    }
    
    @objc public func releaseRegisterChannel() {
        self.callBackChannel = nil
        self.routerChannel = nil
        self.bridgeChannel = nil
        self.removeRouterChannel = nil
    }

}
