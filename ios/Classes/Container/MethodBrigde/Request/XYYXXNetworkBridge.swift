//
//  XYYXXNetworkBridge.swift
//  XYYContainer
//
//  Created by FW on 2020/10/23.
//

import UIKit

class XYYXXNetworkBridge: XYYXXBridge {
    
    override class func bridgeMethodName() -> String {
        return "network"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXNetworkBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        if let interface = XYYXXBridgeRegistry.bridgeInterface {
            if let arguments = bridgeCall.call.arguments as? [String: Any] {
                guard let path = arguments["path"] as? String else {
                    let error = FlutterError(code: "-1", message: "请检查请求路径是否正确", details: nil)
                    bridgeCall.bridgeResult(error)
                    return
                }
                
                let method = arguments["method"] as? String
                let contentType = arguments["content_type"] as? String
                let additionalHeader = arguments["header"] as? [String: String]
                let parameters = arguments["parameters"]
                
                interface.request(withPath: path, withMethod: method, withContentType: contentType, withParameters: parameters, withHeaders: additionalHeader) { (responseJSONStr, responseError) in
                    if responseJSONStr != nil {
                        bridgeCall.bridgeResult(responseJSONStr as Any)
                    } else {
                        let resultError = responseError as NSError?
                        let error = FlutterError(code: "-1", message: resultError?.domain ?? "网络异常，请重试", details: nil)
                        bridgeCall.bridgeResult(error)
                    }
                }
            } else {
                let error = FlutterError(code: "-1", message: "参数非法，请检查参数！", details: nil)
                bridgeCall.bridgeResult(error)
            }
        } else {
            let error = FlutterError(code: "-1", message: "网络桥，实现异常！", details: nil)
            bridgeCall.bridgeResult(error)
        }
    }
    
}
