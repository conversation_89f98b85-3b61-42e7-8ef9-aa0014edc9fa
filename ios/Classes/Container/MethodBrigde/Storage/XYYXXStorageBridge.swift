//
//  XYYXXStorageBridge.swift
//  Pods
//
//  Created by FW on 2020/11/12.
//

import UIKit

class XYYXXStorageBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "storage"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXStorageBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        guard let arguments = bridgeCall.call.arguments as? [String: Any] else {
            let error = FlutterError(code: "-1", message: "参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        guard let space = arguments["space"] as? String else {
            let error = FlutterError(code: "-1", message: "Space参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        guard let type = arguments["type"] as? String else {
            let error = FlutterError(code: "-1", message: "Type参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        let userDefault: UserDefaults = UserDefaults.standard
        var storageMap = userDefault.object(forKey: space) as? [String: Any] ?? [:]
        
        switch type {
        case "putAll":
            /// 存储数据、删除、更新
            if let data = arguments["data"] as? [String: Any] {
                data.forEach { (key, value) in
                    if value is NSNull {
                        /// Value 值为空  删除
                        storageMap.removeValue(forKey: key)
                    } else {
                        /// 覆盖存储 HashMap
                        storageMap[key] = value
                    }
                }
                userDefault.setValue(storageMap, forKey: space)
                userDefault.synchronize()
                bridgeCall.bridgeResult(true)
            }
        case "getAll":
            /// 取全部数据
            bridgeCall.bridgeResult(storageMap)
        case "getValue":
            /// 获取key下的数据
            if let key = arguments["key"] as? String {
                let value = storageMap[key]
                bridgeCall.bridgeResult(value)
            } else {
                let error = FlutterError(code: "-1", message: "Key参数非法，请检查参数！", details: nil)
                bridgeCall.bridgeResult(error)
            }
        case "deleteAll":
            /// 删除命名空间下的所有数据
            userDefault.removeObject(forKey: space)
            userDefault.synchronize()
        default:
            let error = FlutterError(code: "-1", message: "Typ参数非法，不支持此类型！", details: nil)
            bridgeCall.bridgeResult(error)
        }
    }
}
