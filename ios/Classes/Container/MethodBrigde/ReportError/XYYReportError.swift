//
//  XYYReportError.swift
//  XYYContainer
//
//  Created by FW on 2021/1/7.
//

import UIKit

class XYYReportError: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "report_error"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYReportError() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        guard let interface = XYYXXBridgeRegistry.bridgeInterface else {
            print("错误信息上报桥，未实现")
            return
        }
        guard let arguments = bridgeCall.call.arguments as? [String: Any] else { return }
        guard let errorDetail = arguments["errorDetail"] as? String else { return }
        
        interface.reportError?(withInfo: errorDetail)
    }
}
