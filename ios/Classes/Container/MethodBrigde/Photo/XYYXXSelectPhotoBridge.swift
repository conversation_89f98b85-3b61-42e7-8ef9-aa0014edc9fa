//
//  XYYXXSelectPhotoBridge.swift
//  Pods-Runner
//
//  Created by FW on 2020/11/10.
//

import UIKit
import AVFoundation
import Photos
import TZImagePickerController

typealias PhotoBridgeAuthHandler = (_ isAuthed: Bool) -> Void

class XYYXXSelectPhotoBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "select_photo"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXSelectPhotoBridge() as! Self
    }
    
    var bridgeCall: XYYXXBridgeCall!
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        /// 收起键盘
        bridgeCall.engine?.viewController?.view.endEditing(true)
        
        self.bridgeCall = bridgeCall
        if let arguments = bridgeCall.call.arguments as? [String: Any] {
            if let type: String = arguments["type"] as? String {
                switch type {
                case "Camera":
                    self.selectImageForCamera()
                    /// 打开相机同时 申请相册权限 如果不给权限无法获取本地文件id
                    self.checkAlbumAuth { _ in }
                case "Album":
                    if let imageCount = arguments["imageCount"] as? Int {
                        self.selectImageForAlbum(imageCount)
                    } else {
                        self.selectImageForAlbum(1)
                    }
                    break
                default:
                    let error = FlutterError(code: "-1", message: "选择图片类型非法！", details: nil)
                    bridgeCall.bridgeResult(error)
                }
            } else {
                let error = FlutterError(code: "-1", message: "选择图片类型非法！", details: nil)
                bridgeCall.bridgeResult(error)
            }
        } else {
            let error = FlutterError(code: "-1", message: "参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
        }
    }
    
    // MARK: - 调用相机
    private func selectImageForCamera() {
        self.checkCameraAuth { (isAuthed) in
            if isAuthed {
                self.openCamera()
            }
        }
    }
    
    // MARK: - 调用相册
    private func selectImageForAlbum(_ imageCount: Int) {
        self.checkAlbumAuth { (isAuthed) in
            if isAuthed {
                self.openAlbum(imageCount)
            }
        }
    }
    
    // MARK: - 检测相机权限
    private func checkCameraAuth(_ authedHandler: @escaping PhotoBridgeAuthHandler) {
        if AVCaptureDevice.default(for: .video) != nil {
            /// 获取相机权限状态
            let status = AVCaptureDevice.authorizationStatus(for: .video)
            if status == .notDetermined {
                /// 尚未进行授权选择
                AVCaptureDevice.requestAccess(for: .video) { (granted) in
                    DispatchQueue.main.async {
                        authedHandler(granted)
                    }
                }
            } else if status == .restricted || status == .denied {
                /// 已拒绝
                self.showAlertForStatusDenied("相机权限未开启，是否去设置中开启？")
                authedHandler(false)
            } else {
                /// 已授权、或受限制
                authedHandler(true)
            }
        } else {
            let error = FlutterError(code: "-1", message: "此设备没有摄像头!", details: nil)
            self.bridgeCall.bridgeResult(error)
        }
    }
    
    // MARK: - 检测相册权限
    private func checkAlbumAuth(_ authedHandler: @escaping PhotoBridgeAuthHandler) {
        PHPhotoLibrary.requestAuthorization { (status) in
            DispatchQueue.main.async {
                if status == PHAuthorizationStatus.denied || status == PHAuthorizationStatus.restricted {
                    self.showAlertForStatusDenied("相册权限未开启，是否去设置中开启？")
                    authedHandler(false)
                } else {
                    authedHandler(true)
                }
            }
        }
    }
    
    private func showAlertForStatusDenied(_ title: String) {
        let alertController = UIAlertController(title: title, message: nil, preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        alertController.addAction(UIAlertAction(title: "去设置", style: .default, handler: { (action) in
            if let settingURL = URL(string: UIApplication.openSettingsURLString) {
                if UIApplication.shared.canOpenURL(settingURL) {
                    UIApplication.shared.openURL(settingURL)
                }
            }
        }))
        self.bridgeCall.engine?.viewController?.present(alertController, animated: true, completion: nil)
    }
    
    /// 修复图片旋转
    fileprivate func fixImageOrientation(_ originalImage: UIImage) -> UIImage {
        if originalImage.imageOrientation == .up {
            return originalImage
        }
        var transform = CGAffineTransform.identity
        switch originalImage.imageOrientation {
        case .down, .downMirrored:
            transform = transform.translatedBy(x: originalImage.size.width, y: originalImage.size.height)
            transform = transform.rotated(by: .pi)
            break
            
        case .left, .leftMirrored:
            transform = transform.translatedBy(x: originalImage.size.width, y: 0)
            transform = transform.rotated(by: .pi / 2)
            break
            
        case .right, .rightMirrored:
            transform = transform.translatedBy(x: 0, y: originalImage.size.height)
            transform = transform.rotated(by: -.pi / 2)
            break
            
        default:
            break
        }
        
        switch originalImage.imageOrientation {
        case .upMirrored, .downMirrored:
            transform = transform.translatedBy(x: originalImage.size.width, y: 0)
            transform = transform.scaledBy(x: -1, y: 1)
            break
            
        case .leftMirrored, .rightMirrored:
            transform = transform.translatedBy(x: originalImage.size.height, y: 0);
            transform = transform.scaledBy(x: -1, y: 1)
            break
            
        default:
            break
        }
        
        let ctx = CGContext(data: nil, width: Int(originalImage.size.width), height: Int(originalImage.size.height), bitsPerComponent: originalImage.cgImage!.bitsPerComponent, bytesPerRow: 0, space: originalImage.cgImage!.colorSpace!, bitmapInfo: originalImage.cgImage!.bitmapInfo.rawValue)
        ctx?.concatenate(transform)
        
        switch originalImage.imageOrientation {
        case .left, .leftMirrored, .right, .rightMirrored:
            ctx?.draw(originalImage.cgImage!, in: CGRect(x: CGFloat(0), y: CGFloat(0), width: CGFloat(originalImage.size.height), height: CGFloat(originalImage.size.width)))
            break
            
        default:
            ctx?.draw(originalImage.cgImage!, in: CGRect(x: CGFloat(0), y: CGFloat(0), width: CGFloat(originalImage.size.width), height: CGFloat(originalImage.size.height)))
            break
        }
        
        let cgimg: CGImage = (ctx?.makeImage())!
        let fixedImage = UIImage(cgImage: cgimg)
        
        return fixedImage
    }
}

// MARK: - 相机选图处理
extension XYYXXSelectPhotoBridge: UINavigationControllerDelegate, UIImagePickerControllerDelegate {
    
    fileprivate func openCamera() {
        let imagePickerController = UIImagePickerController()
        imagePickerController.isEditing = false
        imagePickerController.delegate = self
        imagePickerController.allowsEditing = false
        imagePickerController.modalPresentationStyle = .fullScreen
        imagePickerController.sourceType = .camera
        imagePickerController.navigationBar.isTranslucent = false
        self.bridgeCall.engine?.viewController?.present(imagePickerController, animated: true, completion: nil)
    }
    
    /// 取消回调
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
        self.bridgeCall.bridgeResult([])
    }
    
    /// 拍照后回调
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        if var image = info[.originalImage] as? UIImage {
            /// 修复图片旋转问题
            image = self.fixImageOrientation(image)
            
            TZImageManager.default()?.savePhoto(with: image, completion: { (asset, error) in
                if error == nil {
                    if let asset = asset {
                        let localId = asset.localIdentifier
                        self.bridgeCall.bridgeResult([localId])
                    } else {
                        let error = FlutterError(code: "-1", message: "图片保存失败！", details: nil)
                        self.bridgeCall.bridgeResult(error)
                    }
                } else {
                    let nsError = error as NSError?
                    let resultError = FlutterError(code: "-1", message: nsError?.domain, details: nil)
                    self.bridgeCall.bridgeResult(resultError)
                }
            })
        }
        picker.dismiss(animated: true, completion: nil)
    }
}

// MARK: - 相册调用处理
extension XYYXXSelectPhotoBridge: TZImagePickerControllerDelegate {
    fileprivate func openAlbum(_ imageCount: Int) {
        self.bridgeCall.engine?.viewController?.view.endEditing(true)
        
        if let imagePickerController = TZImagePickerController(maxImagesCount: imageCount, columnNumber: 4, delegate: self, pushPhotoPickerVc: true) {
            imagePickerController.allowPickingVideo = false
            imagePickerController.allowTakeVideo = false
            imagePickerController.allowTakePicture = true
            imagePickerController.modalPresentationStyle = .fullScreen
            imagePickerController.didFinishPickingPhotosHandle = { (photos, assets, isSelectOriginalPhoto) in
                if let phAssets = assets as? [PHAsset] {
                    let localIds = phAssets.map({ $0.localIdentifier })
                    self.bridgeCall.bridgeResult(localIds)
                } else {
                    self.bridgeCall.bridgeResult([])
                }
            }
            imagePickerController.imagePickerControllerDidCancelHandle = {
                self.bridgeCall.bridgeResult([])
            }
            self.bridgeCall.engine?.viewController?.present(imagePickerController, animated: true, completion: nil)
        } else {
            let error = FlutterError(code: "-1", message: "相册未授权，打开失败!", details: nil)
            self.bridgeCall.bridgeResult(error)
        }
    }
}
