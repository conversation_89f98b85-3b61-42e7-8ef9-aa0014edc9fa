//
//  XYYXXLocalImageUploadBridge.swift
//  XYYContainer
//
//  Created by FW on 2020/11/10.
//

import UIKit
import Photos
import TZImagePickerController

class XYYXXLocalImageUploadBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "upload_photo"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXLocalImageUploadBridge() as! Self
    }
    
    var bridgeCall: XYYXXBridgeCall!
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        self.bridgeCall = bridgeCall;
        
        guard let interface = XYYXXBridgeRegistry.bridgeInterface else {
            let error = FlutterError(code: "-1", message: "图片上传桥，实现异常！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        /// 获取参数
        guard let paramester = bridgeCall.call.arguments as? [String: Any] else {
            let error = FlutterError(code: "-1", message: "参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        /// 获取上传路径
        guard let uploadPath = paramester["uploadUrl"] as? String else {
            let error = FlutterError(code: "-1", message: "参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        /// 获取本地文件id
        guard let localIds = paramester["localPaths"] as? [String] else {
            let error = FlutterError(code: "-1", message: "本地文件数组为空！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        guard let limitWidth = paramester["limitWidth"] as? NSNumber else {
            let error = FlutterError(code: "-1", message: "宽度限制参数错误！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        guard let limitHeight = paramester["limitHeight"] as? NSNumber else {
            let error = FlutterError(code: "-1", message: "高度限制参数错误！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        let extraParams: [String: Any]? = paramester["extraParams"] as? [String: Any]
        
        self.uploadImage(interface, uploadPath, localIds, limitWidth, limitHeight, extraParams)
        
    }
    
    private func uploadImage(_ bridgeInterface: XYYXXBridgeInterface,
                             _ uploadPath: String,
                             _ localIds: [String],
                             _ limitWidth: NSNumber,
                             _ limitHeight: NSNumber,
                             _ extraParams: [String: Any]?)
    {
        var imgeUrls: [String] = []
        let group = DispatchGroup()
        group.enter()
        self.fetchLocalImage(localIds) { (images) in
            images.forEach { (originalImage) in
                group.enter()
                if let imageData = self.compressImage(originalImage, limitWidth: limitWidth, limitHeight: limitWidth) {
                    bridgeInterface.uploadImage(withPath: uploadPath, withImageData: imageData,  withExtraParams: extraParams) { (imageUrl, error) in
                        if let imageUrl = imageUrl {
                            imgeUrls.append(imageUrl)
                        }
                        group.leave()
                    }
                }
            }
            group.leave()
        }
        group.notify(queue: DispatchQueue.main) {
            if imgeUrls.count < localIds.count {
                let error = FlutterError(code: "-1", message: "图片上传失败！", details: nil)
                self.bridgeCall.bridgeResult(error)
            } else {
                self.bridgeCall.bridgeResult(imgeUrls)
            }
        }
    }
    
    // MARK: - 获取图片
    private func fetchLocalImage(_ localIds: [String], _ fetchFinished: @escaping (_ images: [UIImage]) -> Void) {
        var originalImage: [UIImage] = []
        let group = DispatchGroup()
        let result: PHFetchResult = PHAsset.fetchAssets(withLocalIdentifiers: localIds, options: nil)
        result.enumerateObjects { (asset, index, stop) in
            group.enter()
            TZImageManager.default()?.getOriginalPhoto(with: asset, newCompletion: { (image, info, isDegraded) in
                if isDegraded == false {
                    /// 原图
                    if let image = image {
                        originalImage.append(image)
                    }
                    group.leave()
                }
            })
        }
        group.notify(queue: DispatchQueue.main) {
            if originalImage.count < localIds.count {
                let error = FlutterError(code: "-1", message: "获取原图失败！", details: nil)
                self.bridgeCall.bridgeResult(error)
            } else {
                fetchFinished(originalImage)
            }
        }
    }
    
    // MARK: - 压缩图片
    private func compressImage(_ image: UIImage, limitWidth: NSNumber, limitHeight: NSNumber) -> Data? {
        let limitLenght: CGFloat = CGFloat(limitWidth.floatValue * limitHeight.floatValue)
        var resultImage = image;
        guard var data = resultImage.jpegData(compressionQuality: 1) else {
            return resultImage.pngData()
        }
        var lastDataLenght: CGFloat = 0
        
        while CGFloat(data.count) > limitLenght && CGFloat(data.count) != lastDataLenght {
            lastDataLenght = CGFloat(data.count)
            let ratio = limitLenght / CGFloat(data.count)
            if ratio <= 0 {
                break;
            }
            let size = CGSize(width: resultImage.size.width * CGFloat(sqrtf(Float(ratio))), height: resultImage.size.height * CGFloat(sqrtf(Float(ratio))))
            UIGraphicsBeginImageContext(size)
            resultImage.draw(in: CGRect(origin: .zero, size: size))
            if let image = UIGraphicsGetImageFromCurrentImageContext() {
                resultImage = image
            } else {
                return data
            }
            UIGraphicsEndImageContext()
            if let drawData = resultImage.jpegData(compressionQuality: 1) {
                data = drawData
            } else {
                return data
            }
        }
        return data
    }
    
    
}
