//
//  XYYXXPageStackBridge.swift
//  AFNetworking
//
//  Created by FW on 2021/3/11.
//

import UIKit

class XYYXXPageStackBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "page_stack_change"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXPageStackBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        guard let routers = bridgeCall.call.arguments as? [String] else {
            return
        }
        bridgeCall.engine?.pageStackList = routers
    }
}
