//
//  XYYXXToastBridge.swift
//  Pods
//
//  Created by FW on 2020/11/12.
//

import UIKit

class XYYXXToastBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "toast"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXToastBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        guard let interface = XYYXXBridgeRegistry.bridgeInterface else {
            let error = FlutterError(code: "-1", message: "Toast桥接口，尚未实现!", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        /// 获取参数
        guard let paramester = bridgeCall.call.arguments as? [String: Any] else {
            let error = FlutterError(code: "-1", message: "参数非法，请检查参数！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        guard let message = paramester["msg"] as? String else {
            bridgeCall.bridgeResult(nil)
            return
        }
        
        let type = paramester["type"] as? String ?? "Normal"
        var toastStyle: XYYXXToastType = .normal
        switch type {
        case "Success":
            toastStyle = .success
        case "Error":
            toastStyle = .error
        default:
            toastStyle = .normal
        }
        
        interface.showToast(withMessage: message, with: toastStyle)
        bridgeCall.bridgeResult(nil)
    }
}
