//
//  XYYXXLocationBridge.swift
//  Pods
//
//  Created by FW on 2020/11/25.
//

import UIKit

class XYYXXLocationBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "location"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXLocationBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        guard let interface = XYYXXBridgeRegistry.bridgeInterface else {
            let error = FlutterError(code: "-1", message: "定位桥，实现异常！", details: nil)
            bridgeCall.bridgeResult(error)
            return
        }
        
        interface.requestSingleLocation { (address, latitude, longitude, error) in
            if let error = error {
                let flutterError = FlutterError(code: "\((error as NSError).code)", message: (error as NSError).domain, details: (error as NSError).userInfo)
                bridgeCall.bridgeResult(flutterError)
            } else {
                var paramester: [String: Any] = [:]
                if let address = address {
                    paramester["address"] = address
                }
                paramester["latitude"] = latitude
                paramester["longitude"] = longitude
                bridgeCall.bridgeResult(paramester)
            }
        }
    }
}
