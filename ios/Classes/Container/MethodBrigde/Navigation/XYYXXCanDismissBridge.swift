//
//  XYYXXCanDismissBridge.swift
//  XYYContainer
//
//  Created by FW on 2021/2/23.
//

import UIKit

class XYYXXCanDismissBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "canDismiss"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXCanDismissBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        
        var viewController = UIApplication.shared.delegate?.window??.rootViewController
        while true {
            if viewController is UITabBarController {
                viewController = (viewController as? UITabBarController)?.selectedViewController
            }
            if viewController is UINavigationController {
                viewController = (viewController as? UINavigationController)?.topViewController
            }
            if viewController?.presentedViewController != nil {
                viewController = viewController?.presentedViewController
            } else {
                break
            }
        }
        
        if viewController?.presentingViewController != nil {
            bridgeCall.bridgeResult(true)
        } else {
            bridgeCall.bridgeResult(false)
        }
    }

}
