//
//  XYYXXDismissBridge.swift
//  XYYContainer
//
//  Created by FW on 2021/2/23.
//

import UIKit

class XYYXXDismissBridge: XYYXXBridge {
    override class func bridgeMethodName() -> String {
        return "dismiss"
    }
    
    override class func bridgeDestination() -> Self {
        return XYYXXDismissBridge() as! Self
    }
    
    override func bridgeMessage(with bridgeCall: XYYXXBridgeCall) {
        
        var viewController = UIApplication.shared.delegate?.window??.rootViewController
        while true {
            if viewController is UITabBarController {
                viewController = (viewController as? UITabBarController)?.selectedViewController
            }
            if viewController is UINavigationController {
                viewController = (viewController as? UINavigationController)?.topViewController
            }
            if viewController?.presentedViewController != nil {
                viewController = viewController?.presentedViewController
            } else {
                break
            }
        }
        
        var animated: Bool = true
        
        if let paramester = bridgeCall.call.arguments as? [String: Any] {
            if let callAnimated = paramester["animated"] as? Bool {
                animated = callAnimated
            }
        }
        
        if viewController?.presentingViewController != nil {
            viewController?.dismiss(animated: animated, completion: nil)
        }
    }
}
