//
//  XYYXXPluginRegister.m
//  XYYContainer
//
//  Created by FW on 2021/3/30.
//

#import "XYYXXPluginRegister.h"

@implementation XYYXXPluginRegister

+ (void)registerAllPluginWithRegistry:(NSObject<FlutterPluginRegistry>*)registry {
    Class generatedClass = NSClassFromString(@"GeneratedPluginRegistrant");
    SEL registerSelector = NSSelectorFromString(@"registerWithRegistry:");
    
    if (generatedClass) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
        [generatedClass performSelector:registerSelector withObject:registry];
#pragma clang diagnostic pop
    }
}

@end
