//
//  XYYXXXFlutterViewController.h
//  XYYContainer
//
//  Created by FW on 2021/5/26.
//

#import <Flutter/Flutter.h>

@class XYYXXBridge;

NS_ASSUME_NONNULL_BEGIN

typedef void(^XYYXXFlutterNativeCallback)(NSDictionary * __nullable result);

@interface XYYXXFlutterViewController : FlutterViewController

/// 缓存得调用桥， 以防止释放
@property (nonatomic, strong) NSMutableArray<XYYXXBridge *> * bridgeSets;

/// 页面栈数据 - 用来判断当前Flutter页面是否是栈顶
@property (nonatomic, copy, nullable) NSMutableArray<NSString *> * pageStackList;

- (instancetype)initWithRouter:(NSString *)router reply:(XYYXXFlutterNativeCallback __nullable)reply;

@end

NS_ASSUME_NONNULL_END
