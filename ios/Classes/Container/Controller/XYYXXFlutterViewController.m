//
//  XYYXXXFlutterViewController.m
//  XYYContainer
//
//  Created by FW on 2021/5/26.
//

#import "XYYXXFlutterViewController.h"
#if __has_include(<XYYContainer/XYYContainer-Swift.h>)
#import <XYYContainer/XYYContainer-Swift.h>
#else
#import "XYYContainer-Swift.h"
#endif

#import <XYYContainer/XYYXXPluginRegister.h>

@interface XYYXXFlutterViewController ()<FlutterPluginRegistry>

@property (nonatomic, copy) XYYXXFlutterNativeCallback reply;

@property (nonatomic, copy) NSLock * lock;

@end

@implementation XYYXXFlutterViewController

- (instancetype)initWithRouter:(NSString *)router reply:(XYYXXFlutterNativeCallback __nullable)reply {
    FlutterEngine * engine = [[XYYXXFlutterEnginePool share] getUnUseCacheEngine];
    if (engine != nil) {
        [engine runWithEntrypoint:nil initialRoute:router];
        [engine registerChannel];
        self = [super initWithEngine:engine nibName:nil bundle:nil];
    } else {
        self = [super initWithProject:nil initialRoute:router nibName:nil bundle:nil];
        [self.engine runWithEntrypoint:nil initialRoute:router];
        [self.engine registerChannel];
        [XYYXXPluginRegister registerAllPluginWithRegistry:self];
        __weak typeof(self) weakSelf = self;
        [self setFlutterViewDidRenderCallback:^{
            [[XYYXXFlutterEnginePool share] setCacheEngineWith: weakSelf.engine];
        }];
    }
    self.modalPresentationStyle = UIModalPresentationFullScreen;
    self.reply = reply;
    return self;
}

#pragma mark - Datas
- (NSArray<NSString *> *)pageStackList {
    return self.engine.pageStackList;
}

- (void)setBridgeSets:(NSMutableArray<XYYXXBridge *> *)bridgeSets {
    [self.lock lock];
    _bridgeSets = bridgeSets;
    [self.lock unlock];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (self.reply) {
        __weak typeof(self) weakSelf = self;
        [self.engine.callBackChannel invokeMethod:@"" arguments:nil result:^(id  _Nullable result) {
            if (weakSelf.reply) {
                weakSelf.reply(result);
            }
        }];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
}

- (nullable NSObject<FlutterPluginRegistrar> *)registrarForPlugin:(NSString *)pluginKey {
    NSObject<FlutterPluginRegistrar> * registrar = [super registrarForPlugin:pluginKey];
    NSMutableDictionary * registrars = [NSMutableDictionary dictionaryWithDictionary:self.engine.cacheRegistrars];
    registrars[pluginKey] = registrar;
    self.engine.cacheRegistrars = registrars;
    return registrar;
//    return [super registrarForPlugin:pluginKey];
}

- (BOOL)loadDefaultSplashScreenView {
    return NO;
}

- (void)destroyEngine {
    if (self.engine != nil) {
        [self.engine releaseRegisterChannel];
        self.engine.viewController = nil;
        [self.engine destroyContext];
        [[XYYXXFlutterEnginePool share] removeEngineFor:self.engine];
    }
}

- (void)dealloc {
    [self.engine removeRouter];
    [self destroyEngine];
}

#pragma mark - 懒加载
- (NSLock *)lock {
    if (!_lock) {
        _lock = [[NSLock alloc] init];
    }
    return _lock;
}
@end
