#import "ContainerPlugin.h"
#if __has_include(<XYYContainer/XYYContainer-Swift.h>)
#import <XYYContainer/XYYContainer-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "XYYContainer-Swift.h"
#endif

@implementation ContainerPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftContainerPlugin registerWithRegistrar:registrar];
}
@end
